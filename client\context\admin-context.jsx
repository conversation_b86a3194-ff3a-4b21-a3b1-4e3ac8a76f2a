"use client"

import { createContext, useState, useContext, useEffect } from "react"
import { useAuth } from "./auth-context"

const AdminContext = createContext(null)

export const AdminProvider = ({ children }) => {
  const [admin, setAdmin] = useState(null)
  const [loading, setLoading] = useState(true)
  const authContext = useAuth()

  useEffect(() => {
    if (!authContext) {
      setLoading(false)
      return
    }

    const { user } = authContext

    if (user) {
      // Check if user is admin based on email or role
      const isAdmin = user.email === "<EMAIL>" || user.role === "admin"
      if (isAdmin) {
        setAdmin({
          ...user,
          role: "admin",
          permissions: ["manage_users", "manage_events", "view_analytics", "manage_organizers"],
        })
      } else {
        setAdmin(null)
      }
    } else {
      setAdmin(null)
    }
    setLoading(false)
  }, [authContext]) // Updated to use the entire authContext object

  return <AdminContext.Provider value={{ admin, loading }}>{children}</AdminContext.Provider>
}

export const useAdmin = () => {
  const context = useContext(AdminContext)
  if (context === undefined) {
    throw new Error("useAdmin must be used within an AdminProvider")
  }
  return context
}
